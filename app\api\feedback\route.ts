import { type NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase"

export async function POST(request: NextRequest) {
  try {
    const feedbackData = await request.json()
    const supabase = createServerClient()

    const { data, error } = await supabase.from("feedback").insert([feedbackData]).select().single()

    if (error) {
      console.error("Database error:", error)
      return NextResponse.json({ error: error.message }, { status: 400 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("API error:", error)
    return NextResponse.json({ error: "创建反馈失败" }, { status: 500 })
  }
}
