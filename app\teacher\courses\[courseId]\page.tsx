import { getCurrentUser } from "@/lib/auth-actions"
import { createServerClient } from "@/lib/supabase"
import { redirect } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { BookOpen, Users, Calendar, Plus, ArrowLeft, User, FileText } from "lucide-react"
import Link from "next/link"
import { LogoutButton } from "@/components/logout-button"

export default async function CourseDetailsPage({ params }: { params: { courseId: string } }) {
  const user = await getCurrentUser()

  if (!user || user.role !== "teacher") {
    redirect("/login")
  }

  const supabase = createServerClient()

  // Get course details
  const { data: course } = await supabase
    .from("courses")
    .select(`
      *,
      class_sessions (
        id,
        session_number,
        session_date,
        topic
      ),
      course_enrollments (
        student_id,
        users (
          id,
          full_name,
          email
        )
      )
    `)
    .eq("id", params.courseId)
    .eq("teacher_id", user.id)
    .single()

  if (!course) {
    redirect("/teacher/dashboard")
  }

  // Get all feedback for this course
  const { data: feedback } = await supabase
    .from("feedback")
    .select(`
      *,
      users (
        full_name
      ),
      class_sessions (
        session_number,
        topic,
        session_date
      )
    `)
    .eq("teacher_id", user.id)
    .in("session_id", course.class_sessions?.map((s) => s.id) || [])
    .order("created_at", { ascending: false })

  const students = course.course_enrollments?.map((e) => e.users) || []

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <Button variant="ghost" size="sm" asChild>
                <Link href="/teacher/dashboard">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  返回仪表板
                </Link>
              </Button>
              <div className="p-2 bg-blue-600 rounded-lg">
                <BookOpen className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">课程详情</h1>
                <p className="text-sm text-gray-600">{course.name}</p>
              </div>
            </div>
            <LogoutButton />
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Course Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">课程次数</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{course.class_sessions?.length || 0}</div>
              <p className="text-xs text-muted-foreground">已安排的课程</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">学生人数</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{students.length}</div>
              <p className="text-xs text-muted-foreground">已注册学生</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">反馈总数</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{feedback?.length || 0}</div>
              <p className="text-xs text-muted-foreground">已提供的反馈</p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Students List */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    学生名单
                  </CardTitle>
                  <CardDescription>查看所有注册学生</CardDescription>
                </div>
                <Button size="sm" asChild>
                  <Link href={`/teacher/courses/${params.courseId}/feedback/new`}>
                    <Plus className="h-4 w-4 mr-1" />
                    添加反馈
                  </Link>
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              {students.map((student) => {
                const studentFeedbackCount = feedback?.filter((f) => f.student_id === student.id).length || 0
                const latestFeedback = feedback?.find((f) => f.student_id === student.id)

                return (
                  <div key={student.id} className="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <User className="h-4 w-4 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{student.full_name}</h4>
                          <p className="text-sm text-gray-500">{student.email}</p>
                        </div>
                      </div>
                      <Badge variant="secondary">{studentFeedbackCount} 条反馈</Badge>
                    </div>
                    {latestFeedback && (
                      <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
                        <p className="text-gray-600">最新反馈: {latestFeedback.assignment_title}</p>
                        <p className="text-gray-500">成绩: {latestFeedback.grade}</p>
                      </div>
                    )}
                  </div>
                )
              })}
            </CardContent>
          </Card>

          {/* Feedback History */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                反馈历史
              </CardTitle>
              <CardDescription>查看所有学生的反馈记录</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {feedback?.map((item) => (
                <div key={item.id} className="p-6 border rounded-lg bg-white">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{item.assignment_title}</h3>
                      <p className="text-sm text-gray-600">
                        学生: {item.users?.full_name} • 第{item.class_sessions?.session_number}次课
                      </p>
                      <p className="text-sm text-gray-500">{item.class_sessions?.topic}</p>
                    </div>
                    <div className="text-right">
                      <Badge
                        variant={
                          item.grade?.startsWith("A")
                            ? "default"
                            : item.grade?.startsWith("B")
                              ? "secondary"
                              : "outline"
                        }
                        className="text-lg px-3 py-1"
                      >
                        {item.grade}
                      </Badge>
                      <p className="text-xs text-gray-500 mt-1">
                        {new Date(item.class_sessions?.session_date || item.created_at).toLocaleDateString("zh-CN")}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">总体反馈</h4>
                      <p className="text-gray-700 leading-relaxed bg-gray-50 p-3 rounded-md">{item.feedback_content}</p>
                    </div>

                    {item.strengths && (
                      <div>
                        <h4 className="font-medium text-green-800 mb-2">优点</h4>
                        <p className="text-green-700 bg-green-50 p-3 rounded-md">{item.strengths}</p>
                      </div>
                    )}

                    {item.areas_for_improvement && (
                      <div>
                        <h4 className="font-medium text-amber-800 mb-2">改进建议</h4>
                        <p className="text-amber-700 bg-amber-50 p-3 rounded-md">{item.areas_for_improvement}</p>
                      </div>
                    )}
                  </div>
                </div>
              ))}

              {!feedback?.length && (
                <div className="text-center py-8 text-gray-500">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>暂无反馈记录</p>
                  <p className="text-sm">开始为学生作业提供反馈吧！</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
