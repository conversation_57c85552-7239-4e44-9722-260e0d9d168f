import { getCurrentUser } from "@/lib/auth-actions"
import { createServerClient } from "@/lib/supabase"
import { redirect } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { BookOpen, Calendar, TrendingUp, Award } from "lucide-react"
import { LogoutButton } from "@/components/logout-button"

export default async function StudentDashboard() {
  const user = await getCurrentUser()

  if (!user || user.role !== "student") {
    redirect("/login")
  }

  const supabase = createServerClient()

  // Get student's enrolled courses
  const { data: enrollments } = await supabase
    .from("course_enrollments")
    .select(`
      courses (
        id,
        name,
        description,
        users (
          full_name
        )
      )
    `)
    .eq("student_id", user.id)

  // Get student's feedback
  const { data: feedback } = await supabase
    .from("feedback")
    .select(`
      *,
      class_sessions (
        session_number,
        session_date,
        topic,
        courses (
          name
        )
      )
    `)
    .eq("student_id", user.id)
    .order("created_at", { ascending: false })

  // Calculate stats
  const totalFeedback = feedback?.length || 0
  const averageGrade = feedback?.length
    ? feedback.reduce((acc, f) => {
        const gradeValue = f.grade?.startsWith("A")
          ? 4
          : f.grade?.startsWith("B")
            ? 3
            : f.grade?.startsWith("C")
              ? 2
              : 1
        return acc + gradeValue
      }, 0) / feedback.length
    : 0

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-600 rounded-lg">
                <BookOpen className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">Student Dashboard</h1>
                <p className="text-sm text-gray-600">Welcome back, {user.full_name}</p>
              </div>
            </div>
            <LogoutButton />
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Enrolled Courses</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{enrollments?.length || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Feedback</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalFeedback}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Grade</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {averageGrade > 3.5 ? "A" : averageGrade > 2.5 ? "B" : averageGrade > 1.5 ? "C" : "D"}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Recent Activity</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {feedback?.filter((f) => {
                  const weekAgo = new Date()
                  weekAgo.setDate(weekAgo.getDate() - 7)
                  return new Date(f.created_at) > weekAgo
                }).length || 0}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle>Your Courses</CardTitle>
              <CardDescription>Courses you're currently enrolled in</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {enrollments?.map((enrollment) => (
                <div key={enrollment.courses.id} className="p-4 border rounded-lg">
                  <h3 className="font-semibold mb-1">{enrollment.courses.name}</h3>
                  <p className="text-sm text-gray-600 mb-2">{enrollment.courses.description}</p>
                  <p className="text-xs text-gray-500">Instructor: {enrollment.courses.users?.full_name}</p>
                </div>
              ))}
            </CardContent>
          </Card>

          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Your Feedback History</CardTitle>
              <CardDescription>All feedback and grades from your teachers</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {feedback?.map((item) => (
                <div key={item.id} className="p-6 border rounded-lg bg-white">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{item.assignment_title}</h3>
                      <p className="text-sm text-gray-600">
                        {item.class_sessions?.courses?.name} - Session {item.class_sessions?.session_number}
                      </p>
                      <p className="text-sm text-gray-500">{item.class_sessions?.topic}</p>
                    </div>
                    <div className="text-right">
                      <Badge
                        variant={
                          item.grade?.startsWith("A")
                            ? "default"
                            : item.grade?.startsWith("B")
                              ? "secondary"
                              : "outline"
                        }
                        className="text-lg px-3 py-1"
                      >
                        {item.grade}
                      </Badge>
                      <p className="text-xs text-gray-500 mt-1">{new Date(item.created_at).toLocaleDateString()}</p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Feedback</h4>
                      <p className="text-gray-700 leading-relaxed">{item.feedback_content}</p>
                    </div>

                    {item.strengths && (
                      <div>
                        <h4 className="font-medium text-green-800 mb-2">Strengths</h4>
                        <p className="text-green-700 bg-green-50 p-3 rounded-md">{item.strengths}</p>
                      </div>
                    )}

                    {item.areas_for_improvement && (
                      <div>
                        <h4 className="font-medium text-amber-800 mb-2">Areas for Improvement</h4>
                        <p className="text-amber-700 bg-amber-50 p-3 rounded-md">{item.areas_for_improvement}</p>
                      </div>
                    )}
                  </div>
                </div>
              ))}

              {!feedback?.length && (
                <div className="text-center py-8 text-gray-500">
                  <BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No feedback available yet. Check back after your teacher provides feedback on your assignments.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
