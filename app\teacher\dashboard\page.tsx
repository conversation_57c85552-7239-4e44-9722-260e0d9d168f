import { getCurrentUser } from "@/lib/auth-actions"
import { createServerClient } from "@/lib/supabase"
import { redirect } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { BookOpen, Users, Calendar, Plus } from "lucide-react"
import Link from "next/link"
import { LogoutButton } from "@/components/logout-button"

export default async function TeacherDashboard() {
  const user = await getCurrentUser()

  if (!user || user.role !== "teacher") {
    redirect("/login")
  }

  const supabase = createServerClient()

  // Get teacher's courses
  const { data: courses } = await supabase
    .from("courses")
    .select(`
      *,
      class_sessions (
        id,
        session_number,
        session_date,
        topic
      ),
      course_enrollments (
        student_id,
        users (
          full_name,
          email
        )
      )
    `)
    .eq("teacher_id", user.id)

  // Get recent feedback
  const { data: recentFeedback } = await supabase
    .from("feedback")
    .select(`
      *,
      users (
        full_name
      ),
      class_sessions (
        session_number,
        topic,
        courses (
          name
        )
      )
    `)
    .eq("teacher_id", user.id)
    .order("created_at", { ascending: false })
    .limit(5)

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-600 rounded-lg">
                <BookOpen className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">教师工作台</h1>
                <p className="text-sm text-gray-600">作业反馈管理系统, {user.full_name}</p>
              </div>
            </div>
            <LogoutButton />
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">课程总数</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{courses?.length || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">学生总数</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {courses?.reduce((acc, course) => acc + (course.course_enrollments?.length || 0), 0) || 0}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">最新反馈</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{recentFeedback?.length || 0}</div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <Card>
            <CardHeader>
              <CardTitle>我的课程</CardTitle>
              <CardDescription>管理您的课程和课堂</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {courses?.map((course) => (
                <div key={course.id} className="p-4 border rounded-lg">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-semibold">{course.name}</h3>
                    <Badge variant="secondary">{course.course_enrollments?.length || 0} students</Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">{course.description}</p>
                  <div className="flex space-x-2">
                    <Button size="sm" asChild>
                      <Link href={`/teacher/courses/${course.id}`}>查看详情</Link>
                    </Button>
                    <Button size="sm" variant="outline" asChild>
                      <Link href={`/teacher/courses/${course.id}/feedback/new`}>
                        <Plus className="h-4 w-4 mr-1" />
                        添加反馈
                      </Link>
                    </Button>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>最新反馈</CardTitle>
              <CardDescription>您最近提交的学生反馈</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {recentFeedback?.map((feedback) => (
                <div key={feedback.id} className="p-4 border rounded-lg">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium">{feedback.assignment_title}</h4>
                    <Badge variant={feedback.grade?.startsWith("A") ? "default" : "secondary"}>{feedback.grade}</Badge>
                  </div>
                  <p className="text-sm text-gray-600">Student: {feedback.users?.full_name}</p>
                  <p className="text-sm text-gray-600">Course: {feedback.class_sessions?.courses?.name}</p>
                  <p className="text-xs text-gray-500 mt-2">{new Date(feedback.created_at).toLocaleDateString()}</p>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
