"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardDescription, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Users, User, Calendar, FileText, Save, CheckCircle } from "lucide-react"
import { useRouter } from "next/navigation"

interface Student {
  id: string
  full_name: string
  email: string
}

interface Session {
  id: string
  session_number: number
  session_date: string
  topic: string
}

interface AddFeedbackFormProps {
  courseId: string
  students: Student[]
  sessions: Session[]
  teacherId: string
}

export function AddFeedbackForm({ courseId, students, sessions, teacherId }: AddFeedbackFormProps) {
  const [selectedStudents, setSelectedStudents] = useState<string[]>([])
  const [selectedSession, setSelectedSession] = useState<string>("")
  const [assignmentTitle, setAssignmentTitle] = useState("")
  const [feedbackContent, setFeedbackContent] = useState("")
  const [grade, setGrade] = useState("")
  const [strengths, setStrengths] = useState("")
  const [improvements, setImprovements] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [success, setSuccess] = useState(false)

  const router = useRouter()

  const handleStudentToggle = (studentId: string) => {
    setSelectedStudents((prev) =>
      prev.includes(studentId) ? prev.filter((id) => id !== studentId) : [...prev, studentId],
    )
  }

  const handleSelectAll = () => {
    if (selectedStudents.length === students.length) {
      setSelectedStudents([])
    } else {
      setSelectedStudents(students.map((s) => s.id))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Create feedback for each selected student
      const feedbackPromises = selectedStudents.map(async (studentId) => {
        const response = await fetch("/api/feedback", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            session_id: selectedSession,
            student_id: studentId,
            teacher_id: teacherId,
            assignment_title: assignmentTitle,
            feedback_content: feedbackContent,
            grade,
            strengths,
            areas_for_improvement: improvements,
          }),
        })

        if (!response.ok) {
          throw new Error("Failed to create feedback")
        }

        return response.json()
      })

      await Promise.all(feedbackPromises)
      setSuccess(true)

      // Reset form after success
      setTimeout(() => {
        router.push(`/teacher/courses/${courseId}`)
      }, 2000)
    } catch (error) {
      console.error("Error creating feedback:", error)
      alert("创建反馈时出错，请重试")
    } finally {
      setIsLoading(false)
    }
  }

  if (success) {
    return (
      <div className="text-center py-8">
        <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">反馈创建成功！</h3>
        <p className="text-gray-600">已为 {selectedStudents.length} 名学生创建反馈</p>
        <p className="text-sm text-gray-500 mt-2">正在返回课程详情页面...</p>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* Session Selection */}
      <div className="space-y-4">
        <div>
          <Label className="text-base font-semibold flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            选择课程次数
          </Label>
          <p className="text-sm text-gray-600 mb-3">选择要为哪次课程的作业提供反馈</p>
        </div>
        <Select value={selectedSession} onValueChange={setSelectedSession} required>
          <SelectTrigger>
            <SelectValue placeholder="请选择课程次数" />
          </SelectTrigger>
          <SelectContent>
            {sessions.map((session) => (
              <SelectItem key={session.id} value={session.id}>
                第{session.session_number}次课 - {session.topic} (
                {new Date(session.session_date).toLocaleDateString("zh-CN")})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Student Selection */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div>
            <Label className="text-base font-semibold flex items-center gap-2">
              <Users className="h-4 w-4" />
              选择学生
            </Label>
            <p className="text-sm text-gray-600">选择要为其提供反馈的学生</p>
          </div>
          <Button type="button" variant="outline" size="sm" onClick={handleSelectAll}>
            {selectedStudents.length === students.length ? "取消全选" : "全选"}
          </Button>
        </div>

        <Card>
          <CardHeader className="pb-3">
            <CardDescription>
              已选择 {selectedStudents.length} / {students.length} 名学生
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {students.map((student) => (
                <div
                  key={student.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedStudents.includes(student.id)
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-200 hover:bg-gray-50"
                  }`}
                  onClick={() => handleStudentToggle(student.id)}
                >
                  <div className="flex items-center space-x-3">
                    <Checkbox checked={selectedStudents.includes(student.id)} readOnly />
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <User className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{student.full_name}</p>
                        <p className="text-sm text-gray-500">{student.email}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Assignment Details */}
      <div className="space-y-4">
        <Label className="text-base font-semibold flex items-center gap-2">
          <FileText className="h-4 w-4" />
          作业信息
        </Label>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="assignment-title">作业标题 *</Label>
            <Input
              id="assignment-title"
              value={assignmentTitle}
              onChange={(e) => setAssignmentTitle(e.target.value)}
              placeholder="例如：第三章练习题"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="grade">成绩</Label>
            <Select value={grade} onValueChange={setGrade}>
              <SelectTrigger>
                <SelectValue placeholder="选择成绩等级" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="A+">A+ (优秀+)</SelectItem>
                <SelectItem value="A">A (优秀)</SelectItem>
                <SelectItem value="A-">A- (优秀-)</SelectItem>
                <SelectItem value="B+">B+ (良好+)</SelectItem>
                <SelectItem value="B">B (良好)</SelectItem>
                <SelectItem value="B-">B- (良好-)</SelectItem>
                <SelectItem value="C+">C+ (中等+)</SelectItem>
                <SelectItem value="C">C (中等)</SelectItem>
                <SelectItem value="C-">C- (中等-)</SelectItem>
                <SelectItem value="D">D (及格)</SelectItem>
                <SelectItem value="F">F (不及格)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Feedback Content */}
      <div className="space-y-6">
        <Label className="text-base font-semibold">反馈内容</Label>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="feedback-content">总体反馈 *</Label>
            <Textarea
              id="feedback-content"
              value={feedbackContent}
              onChange={(e) => setFeedbackContent(e.target.value)}
              placeholder="请提供对学生作业的总体评价和反馈..."
              className="min-h-[120px]"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="strengths">优点和亮点</Label>
            <Textarea
              id="strengths"
              value={strengths}
              onChange={(e) => setStrengths(e.target.value)}
              placeholder="描述学生在这次作业中表现出色的地方..."
              className="min-h-[100px]"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="improvements">改进建议</Label>
            <Textarea
              id="improvements"
              value={improvements}
              onChange={(e) => setImprovements(e.target.value)}
              placeholder="提供具体的改进建议和学习方向..."
              className="min-h-[100px]"
            />
          </div>
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end space-x-4 pt-6 border-t">
        <Button type="button" variant="outline" onClick={() => router.back()}>
          取消
        </Button>
        <Button
          type="submit"
          disabled={
            isLoading || selectedStudents.length === 0 || !selectedSession || !assignmentTitle || !feedbackContent
          }
          className="min-w-[120px]"
        >
          {isLoading ? (
            "提交中..."
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              提交反馈
            </>
          )}
        </Button>
      </div>

      {selectedStudents.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <p className="text-sm text-blue-800">
            <strong>提示：</strong>将为以下 {selectedStudents.length} 名学生创建相同的反馈内容：
          </p>
          <div className="flex flex-wrap gap-2 mt-2">
            {selectedStudents.map((studentId) => {
              const student = students.find((s) => s.id === studentId)
              return (
                <Badge key={studentId} variant="secondary">
                  {student?.full_name}
                </Badge>
              )
            })}
          </div>
        </div>
      )}
    </form>
  )
}
