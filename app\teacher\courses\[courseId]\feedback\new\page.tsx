import { getCurrentUser } from "@/lib/auth-actions"
import { createServerClient } from "@/lib/supabase"
import { redirect } from "next/navigation"
import { AddFeedbackForm } from "@/components/add-feedback-form"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { BookOpen, ArrowLeft } from "lucide-react"
import Link from "next/link"
import { LogoutButton } from "@/components/logout-button"

export default async function AddFeedbackPage({ params }: { params: { courseId: string } }) {
  const user = await getCurrentUser()

  if (!user || user.role !== "teacher") {
    redirect("/login")
  }

  const supabase = createServerClient()

  // Get course details with students and sessions
  const { data: course } = await supabase
    .from("courses")
    .select(`
      *,
      class_sessions (
        id,
        session_number,
        session_date,
        topic
      ),
      course_enrollments (
        student_id,
        users (
          id,
          full_name,
          email
        )
      )
    `)
    .eq("id", params.courseId)
    .eq("teacher_id", user.id)
    .single()

  if (!course) {
    redirect("/teacher/dashboard")
  }

  const students = course.course_enrollments?.map((e) => e.users) || []
  const sessions = course.class_sessions || []

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <Button variant="ghost" size="sm" asChild>
                <Link href={`/teacher/courses/${params.courseId}`}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  返回课程详情
                </Link>
              </Button>
              <div className="p-2 bg-green-600 rounded-lg">
                <BookOpen className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">添加反馈</h1>
                <p className="text-sm text-gray-600">{course.name}</p>
              </div>
            </div>
            <LogoutButton />
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Card>
          <CardHeader>
            <CardTitle>为学生作业提供反馈</CardTitle>
            <CardDescription>选择学生并为他们的作业提供详细的反馈和评价</CardDescription>
          </CardHeader>
          <CardContent>
            <AddFeedbackForm courseId={params.courseId} students={students} sessions={sessions} teacherId={user.id} />
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
