-- Insert sample users (passwords are hashed version of 'password123')
INSERT INTO users (id, email, password_hash, full_name, role) VALUES
('550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', 'Dr. <PERSON>', 'teacher'),
('550e8400-e29b-41d4-a716-446655440002', '<EMAIL>', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', 'Prof<PERSON> <PERSON>', 'teacher'),
('550e8400-e29b-41d4-a716-446655440003', '<EMAIL>', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', '<PERSON>', 'student'),
('550e8400-e29b-41d4-a716-446655440004', '<EMAIL>', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', 'Bob Wilson', 'student'),
('550e8400-e29b-41d4-a716-446655440005', '<EMAIL>', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', 'Carol Davis', 'student');

-- Insert sample courses
INSERT INTO courses (id, name, description, teacher_id) VALUES
('650e8400-e29b-41d4-a716-446655440001', 'Advanced Mathematics', 'Calculus and Linear Algebra', '550e8400-e29b-41d4-a716-446655440001'),
('650e8400-e29b-41d4-a716-446655440002', 'Computer Science Fundamentals', 'Introduction to Programming and Algorithms', '550e8400-e29b-41d4-a716-446655440002'),
('650e8400-e29b-41d4-a716-446655440003', 'English Literature', 'Modern American Literature', '550e8400-e29b-41d4-a716-446655440001');

-- Insert course enrollments
INSERT INTO course_enrollments (course_id, student_id) VALUES
('650e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440003'),
('650e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440004'),
('650e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440003'),
('650e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440005'),
('650e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440004');

-- Insert class sessions
INSERT INTO class_sessions (id, course_id, session_number, session_date, topic) VALUES
('750e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440001', 1, '2024-01-15', 'Introduction to Limits'),
('750e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-446655440001', 2, '2024-01-22', 'Derivatives and Applications'),
('750e8400-e29b-41d4-a716-446655440003', '650e8400-e29b-41d4-a716-446655440002', 1, '2024-01-16', 'Variables and Data Types'),
('750e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440002', 2, '2024-01-23', 'Control Structures');

-- Insert sample feedback
INSERT INTO feedback (session_id, student_id, teacher_id, assignment_title, feedback_content, grade, strengths, areas_for_improvement) VALUES
('750e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440001', 'Limits Problem Set 1', 'Excellent work on understanding the concept of limits. Your solutions show clear mathematical reasoning and proper notation.', 'A', 'Strong grasp of limit concepts, clear mathematical notation, well-organized solutions', 'Work on more complex limit problems involving trigonometric functions'),
('750e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440001', 'Derivative Applications', 'Good understanding of basic derivatives. Need to focus more on application problems.', 'B+', 'Solid foundation in derivative rules', 'Apply derivatives to real-world problems, practice optimization'),
('750e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440002', 'Python Basics Assignment', 'Great job on your first programming assignment! Your code is clean and well-commented.', 'A-', 'Clean code structure, good commenting practices, logical problem-solving approach', 'Focus on error handling and edge cases in your solutions');
