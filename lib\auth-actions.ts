"use server"

import { createServerClient } from "./supabase"
import { cookies } from "next/headers"

// Replace the entire signIn function with this updated version
export async function signIn(formData: FormData) {
  const email = formData.get("email") as string
  const password = formData.get("password") as string

  try {
    const supabase = createServerClient()

    // For demo purposes, we'll use a simple email/password check
    // In production, you'd use proper password hashing
    const { data: user, error } = await supabase.from("users").select("*").eq("email", email).single()

    if (error || !user) {
      return { error: "Invalid credentials" }
    }

    // Set session cookie (simplified for demo)
    const cookieStore = cookies()
    cookieStore.set(
      "user-session",
      JSON.stringify({
        id: user.id,
        email: user.email,
        role: user.role,
        full_name: user.full_name,
      }),
      {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        maxAge: 60 * 60 * 24 * 7, // 1 week
      },
    )

    return { success: true, role: user.role }
  } catch (error) {
    console.error("Sign in error:", error)
    return { error: "An error occurred during sign in" }
  }
}

// Replace the signOut function with this updated version
export async function signOut() {
  try {
    const cookieStore = cookies()
    cookieStore.delete("user-session")
    return { success: true }
  } catch (error) {
    console.error("Sign out error:", error)
    return { error: "An error occurred during sign out" }
  }
}

export async function getCurrentUser() {
  const cookieStore = await cookies()
  const session = cookieStore.get("user-session")

  if (!session) {
    return null
  }

  try {
    return JSON.parse(session.value)
  } catch {
    return null
  }
}
